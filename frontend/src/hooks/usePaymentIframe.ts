import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { validateIframeToken } from "../services/api";
import type { PayFieldsConfig, MerchantInfo, PaymentInfo } from "../types/payment";
import { configureIframeBodyStyles, configureIframeViewport, formatErrorMessage } from "../utils/paymentUtils";

interface UsePaymentIframeReturn {
  payFieldsConfig: PayFieldsConfig | null;
  merchantInfo: MerchantInfo | null;
  paymentInfo: PaymentInfo | null;
  error: string | null;
  loading: boolean;
}

/**
 * Custom hook for managing payment iframe initialization and token validation
 */
export const usePaymentIframe = (): UsePaymentIframeReturn => {
  const [searchParams] = useSearchParams();
  const [payFieldsConfig, setPayFieldsConfig] = useState<PayFieldsConfig | null>(null);
  const [merchantInfo, setMerchantInfo] = useState<MerchantInfo | null>(null);
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Configure iframe-specific styling
    configureIframeBodyStyles();
    configureIframeViewport();

    // Get token from URL parameters
    const token = searchParams.get("token");

    if (!token) {
      setError("Missing payment token. Please use a valid payment link.");
      setLoading(false);
      return;
    }

    // Validate token and get payment configuration
    const validateTokenAndLoadConfig = async () => {
      try {
        console.log("Validating token and loading payment configuration:", {
          token: token.substring(0, 8) + "...",
        });

        const data = await validateIframeToken(token);

        if (!data.success || !data.data) {
          throw new Error(data.message || "Invalid token or configuration");
        }

        // Add Google Pay configuration from environment variables
        const enhancedConfig: PayFieldsConfig = {
          ...data.data.config,
          googlePayEnabled: import.meta.env.VITE_GOOGLE_PAY_ENABLED === 'true',
          googlePayEnvironment: import.meta.env.VITE_GOOGLE_PAY_ENVIRONMENT || 'TEST',
          googlePayMerchantId: import.meta.env.VITE_GOOGLE_PAY_MERCHANT_ID,
          googlePayMerchantName: import.meta.env.VITE_GOOGLE_PAY_MERCHANT_NAME || data.data.merchantInfo.name,
        };

        setPayFieldsConfig(enhancedConfig);
        setMerchantInfo(data.data.merchantInfo);
        setPaymentInfo(data.data.paymentInfo);
        setLoading(false);

        // Notify parent window that iframe is ready
        if (window.parent !== window) {
          window.parent.postMessage(
            {
              type: "PAYMENT_IFRAME_READY",
              data: {
                merchantName: data.data.merchantInfo.name,
                amount: data.data.paymentInfo.amount,
                description: data.data.paymentInfo.description,
              },
            },
            "*"
          );
        }
      } catch (error) {
        console.error("Failed to validate token and load payment configuration:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to load payment configuration";
        setError(errorMessage);
        setLoading(false);

        // Notify parent window of error
        if (window.parent !== window) {
          window.parent.postMessage(formatErrorMessage(errorMessage, "PAYMENT_IFRAME_ERROR"), "*");
        }
      }
    };

    validateTokenAndLoadConfig();
  }, [searchParams]);

  return {
    payFieldsConfig,
    merchantInfo,
    paymentInfo,
    error,
    loading,
  };
};
