import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import GooglePayButton from "../GooglePayButton";
import SecurePayFields from "../SecurePayFields";
import { PayFieldsConfig } from "../types/payfields.types";

// Mock the script loader
vi.mock("../utils/script-loader", () => ({
  loadGooglePayScript: vi.fn(),
  loadPayFieldsScript: vi.fn(),
  cleanupPayFieldsScript: vi.fn(),
}));

// Mock the error handler
vi.mock("../utils/google-pay-error-handler", () => ({
  handleGooglePayScriptLoadError: vi.fn(),
  handleGooglePayInitializationError: vi.fn(),
  checkGooglePayAvailability: vi.fn(() => true),
  validateGooglePayConfiguration: vi.fn(() => ({ isValid: true })),
}));

// Mock iframe communication
vi.mock("../utils/iframe-communication", () => ({
  postMessageToParent: vi.fn(),
  isInIframe: vi.fn(() => false),
}));

// Mock payment handlers
vi.mock("../handlers/payment-handlers", () => ({
  createPaymentSuccessHandler: vi.fn(() => vi.fn()),
  createPaymentFailureHandler: vi.fn(() => vi.fn()),
  createValidationFailureHandler: vi.fn(() => vi.fn()),
  createPaymentFinishHandler: vi.fn(() => vi.fn()),
}));

// Mock toast
vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("Google Pay Integration", () => {
  const mockConfig: PayFieldsConfig = {
    merchantId: "test_merchant_123",
    publicKey: "pk_test_123456789",
    amount: 5000,
    description: "Test Payment",
    mode: "token",
    txnType: "auth",
    googlePayEnabled: true,
    googlePayEnvironment: "TEST",
    googlePayMerchantId: "12345678901234567890",
    googlePayMerchantName: "Test Merchant",
    googlePayConfig: {
      enabled: true,
      environment: "TEST",
      merchantId: "12345678901234567890",
      merchantName: "Test Merchant",
      allowedCardNetworks: ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
      allowedAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
      gateway: "payrix",
      gatewayMerchantId: "test_merchant_123",
    },
  };

  beforeEach(() => {
    // Mock window.PayFields
    global.window.PayFields = {
      config: {
        apiKey: "",
        merchant: "",
        mode: "",
        txnType: "",
        amount: 0,
        description: "",
        googlePay: {
          enabled: true,
          environment: "TEST",
          merchantId: "12345678901234567890",
          merchantName: "Test Merchant",
          allowedCardNetworks: ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
          allowedAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
          gateway: "payrix",
          gatewayMerchantId: "test_merchant_123",
        },
      },
      fields: [],
      customizations: {
        style: {},
        placeholders: {},
      },
      submit: vi.fn(),
      ready: vi.fn(),
      init: vi.fn(),
      onSuccess: undefined,
      onFailure: undefined,
      onValidationFailure: undefined,
      onFinish: undefined,
    };

    // Mock Google Pay API
    global.window.google = {
      payments: {
        api: {
          PaymentsClient: vi.fn(),
        },
      },
    };

    // Mock DOM elements
    document.getElementById = vi.fn((id) => {
      const mockElement = document.createElement("div");
      mockElement.id = id;
      return mockElement;
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
    delete global.window.PayFields;
    delete global.window.google;
  });

  describe("GooglePayButton Component", () => {
    it("should render Google Pay button when configuration is valid", async () => {
      const mockOnClick = vi.fn();

      render(<GooglePayButton onClick={mockOnClick} amount={5000} containerClassName="test-container" />);

      await waitFor(() => {
        expect(screen.getByText("Express Checkout")).toBeInTheDocument();
        expect(screen.getByText("$50.00")).toBeInTheDocument();
        expect(screen.getByText("Pay")).toBeInTheDocument();
      });
    });

    it("should handle Google Pay button click", async () => {
      const mockOnClick = vi.fn();

      render(<GooglePayButton onClick={mockOnClick} amount={5000} />);

      await waitFor(() => {
        const payButton = screen.getByText("Pay");
        expect(payButton).toBeInTheDocument();
      });

      const payButton = screen.getByText("Pay");
      fireEvent.click(payButton);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it("should not render when Google Pay is not available", () => {
      delete global.window.PayFields.config.googlePay;

      render(<GooglePayButton onClick={vi.fn()} amount={5000} />);

      expect(screen.queryByText("Express Checkout")).not.toBeInTheDocument();
    });

    it("should be disabled when disabled prop is true", async () => {
      render(<GooglePayButton onClick={vi.fn()} amount={5000} disabled={true} />);

      await waitFor(() => {
        const payButton = screen.getByText("Pay");
        expect(payButton).toBeDisabled();
      });
    });
  });

  describe("SecurePayFields with Google Pay", () => {
    it("should render Google Pay button when enabled in config", () => {
      render(<SecurePayFields config={mockConfig} onSuccess={vi.fn()} onFailure={vi.fn()} />);

      expect(screen.getByText("Express Checkout")).toBeInTheDocument();
      expect(screen.getByText("or pay with card")).toBeInTheDocument();
    });

    it("should not render Google Pay button when disabled in config", () => {
      const configWithoutGooglePay = {
        ...mockConfig,
        googlePayEnabled: false,
        googlePayConfig: undefined,
      };

      render(<SecurePayFields config={configWithoutGooglePay} onSuccess={vi.fn()} onFailure={vi.fn()} />);

      expect(screen.queryByText("Express Checkout")).not.toBeInTheDocument();
      expect(screen.queryByText("or pay with card")).not.toBeInTheDocument();
    });

    it("should render card fields alongside Google Pay", () => {
      render(<SecurePayFields config={mockConfig} onSuccess={vi.fn()} onFailure={vi.fn()} />);

      expect(screen.getByText("Express Checkout")).toBeInTheDocument();
      expect(screen.getByText("Card Number")).toBeInTheDocument();
      expect(screen.getByText("Cardholder Name")).toBeInTheDocument();
      expect(screen.getByText("Expiration Date")).toBeInTheDocument();
      expect(screen.getByText("CVV")).toBeInTheDocument();
    });
  });

  describe("Google Pay Configuration Validation", () => {
    it("should validate required Google Pay configuration fields", () => {
      const { validateGooglePayConfiguration } = require("../utils/google-pay-error-handler");

      const validConfig = {
        environment: "TEST",
        merchantId: "12345678901234567890",
        merchantName: "Test Merchant",
      };

      const result = validateGooglePayConfiguration(validConfig);
      expect(result.isValid).toBe(true);
    });

    it("should reject invalid Google Pay configuration", () => {
      const { validateGooglePayConfiguration } = require("../utils/google-pay-error-handler");

      const invalidConfig = {
        environment: "INVALID",
        merchantId: "",
      };

      const result = validateGooglePayConfiguration(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe("Google Pay Error Handling", () => {
    it("should handle Google Pay script load errors", () => {
      const { handleGooglePayScriptLoadError } = require("../utils/google-pay-error-handler");
      const mockError = new Error("Script load failed");
      const mockOnError = vi.fn();

      const result = handleGooglePayScriptLoadError(mockError, {
        onError: mockOnError,
        showToast: false,
      });

      expect(result).toContain("Failed to load Google Pay");
      expect(mockOnError).toHaveBeenCalled();
    });

    it("should handle Google Pay initialization errors", () => {
      const { handleGooglePayInitializationError } = require("../utils/google-pay-error-handler");
      const mockError = "Initialization failed";
      const mockOnError = vi.fn();

      const result = handleGooglePayInitializationError(mockError, {
        onError: mockOnError,
        showToast: false,
      });

      expect(result).toContain("Google Pay initialization failed");
      expect(mockOnError).toHaveBeenCalled();
    });

    it("should handle Google Pay timeout errors", () => {
      const { handleGooglePayTimeoutError } = require("../utils/google-pay-error-handler");
      const mockOnError = vi.fn();

      const result = handleGooglePayTimeoutError(30000, {
        onError: mockOnError,
        showToast: false,
      });

      expect(result).toContain("Google Pay payment timed out");
      expect(mockOnError).toHaveBeenCalled();
    });
  });

  describe("PayFields Integration", () => {
    it("should configure PayFields with Google Pay settings", () => {
      const { configurePayFields } = require("../utils/payfields-initializer");

      configurePayFields(mockConfig);

      expect(global.window.PayFields.config.googlePay).toBeDefined();
      expect(global.window.PayFields.config.googlePay.enabled).toBe(true);
      expect(global.window.PayFields.config.googlePay.environment).toBe("TEST");
      expect(global.window.PayFields.config.googlePay.gateway).toBe("payrix");
    });

    it("should add Google Pay button to PayFields elements", () => {
      const { setupPayFieldsElements } = require("../utils/payfields-initializer");

      setupPayFieldsElements();

      const googlePayField = global.window.PayFields.fields.find((field: any) => field.type === "googlePay");

      expect(googlePayField).toBeDefined();
      expect(googlePayField.element).toBe("#googlePayButton");
    });
  });
});
