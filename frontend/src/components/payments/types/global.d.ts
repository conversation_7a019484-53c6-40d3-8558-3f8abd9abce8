import { PayFieldsWindow } from "./payfields.types";

interface GooglePaymentsClient {
  isReadyToPay(request: unknown): Promise<{ result: boolean }>;
  createButton(options: unknown): HTMLElement;
  loadPaymentData(request: unknown): Promise<unknown>;
}

interface GooglePayments {
  api: {
    PaymentsClient: new (options: unknown) => GooglePaymentsClient;
  };
}

declare global {
  interface Window {
    PayFields: PayFieldsWindow;
    google?: {
      payments?: GooglePayments;
    };
  }
}
