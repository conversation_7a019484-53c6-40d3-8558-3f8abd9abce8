import visaLogo from '/paymentLogos/visa.png';
import mastercardLogo from '/paymentLogos/mastercard.png';
import amexLogo from '/paymentLogos/amex.png';
import discoverLogo from '/paymentLogos/discover.png';

interface PaymentLogosProps {
  className?: string;
  showGooglePay?: boolean;
}

const PaymentLogos = ({ className = "", showGooglePay = false }: PaymentLogosProps) => {
  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {/* Google Pay */}
      {showGooglePay && (
        <div className="w-12 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="100%"
            height="100%"
            viewBox="0 0 80 40"
            fill="none"
          >
            <rect width="80" height="40" rx="4" fill="white"/>
            <g transform="translate(10, 8)">
              <path d="M22.065 9.162c-.473-.29-1.053-.436-1.632-.436-1.208 0-2.212.657-2.657 1.561-.146.291-.217.653-.217 1.014 0 .363.072.726.217 1.014.436.906 1.449 1.561 2.657 1.561.58 0 1.159-.146 1.632-.436v1.737c-.508.217-1.015.291-1.595.291-1.995 0-3.79-1.305-4.371-3.155-.146-.436-.217-.943-.217-1.449 0-.508.072-1.014.217-1.449.581-1.849 2.376-3.155 4.371-3.155.58 0 1.087.072 1.595.291v1.737zm10.155 2.93H30.08v2.138h-1.232v-2.138h-2.139V10.86h2.139V8.721h1.232v2.139h2.14z" fill="#5F6368"/>
              <path d="M24.168 8.576c.799 0 1.596.217 2.249.653l-1.668 1.668c-.29-.145-.653-.29-1.014-.29-.943 0-1.703.761-1.703 1.703 0 .943.761 1.703 1.703 1.703.799 0 1.449-.507 1.631-1.159h-1.631v-1.885h3.627c.072.29.072.58.072.87 0 2.067-1.377 3.518-3.444 3.518-2.012 0-3.662-1.595-3.662-3.662s1.595-3.662 3.662-3.662l.178.543zm-6.764.145h2.775l-2.484 6.173H16.101l-1.595-4.216h-.036l-1.595 4.216H11.281L13.765 8.721h2.775l1.522 4.07h.036l1.306-4.07z" fill="#4285F4"/>
              <text x="35" y="14" fontFamily="Arial, sans-serif" fontSize="11" fill="#5F6368">Pay</text>
            </g>
          </svg>
        </div>
      )}

      {/* Visa */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={visaLogo} alt="Visa" className="w-full h-auto object-contain" />
      </div>

      {/* Mastercard */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={mastercardLogo} alt="Mastercard" className="w-full h-auto object-contain" />
      </div>

      {/* American Express */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={amexLogo} alt="American Express" className="w-full h-auto object-contain" />
      </div>

      {/* Discover */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={discoverLogo} alt="Discover" className="w-full h-auto object-contain" />
      </div>
    </div>
  );
};

export default PaymentLogos;