import { useEffect, useState, useRef } from "react";

interface GooglePayButtonProps {
  onClick?: () => void;
  disabled?: boolean;
  amount?: number;
  containerClassName?: string;
}

const GooglePayButton = ({ onClick, disabled = false, amount, containerClassName = "" }: GooglePayButtonProps) => {
  const [isGooglePayAvailable, setIsGooglePayAvailable] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Check if Google Pay is configured in PayFields
    if (window.PayFields && window.PayFields.config.googlePay) {
      setIsGooglePayAvailable(true);
    }
  }, []);

  if (!isGooglePayAvailable) {
    return null;
  }

  const handleGooglePayClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  return (
    <div className={`google-pay-button-container ${containerClassName}`}>
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Express Checkout</span>
          {amount && (
            <span className="text-sm font-semibold text-gray-900">
              ${(amount / 100).toFixed(2)}
            </span>
          )}
        </div>
        
        {/* This div will be replaced by the actual Google Pay button from PayFields */}
        <div 
          id="googlePayButton" 
          ref={buttonRef}
          className={`google-pay-button-element ${disabled ? 'opacity-50 pointer-events-none' : ''}`}
        >
          {/* Fallback button while PayFields loads the actual Google Pay button */}
          <button
            type="button"
            className="w-full bg-black text-white py-3 px-4 rounded-md hover:bg-gray-900 transition-colors flex items-center justify-center gap-2"
            disabled={disabled}
            onClick={handleGooglePayClick}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="41"
              height="17"
              viewBox="0 0 41 17"
              fill="currentColor"
              className="h-4 w-auto"
            >
              <g fill="#ffffff">
                <path d="M19.526 6.77l-.936 2.32h-.034l-.971-2.32H16.47l1.459 3.507-.832 1.848h1.08l2.248-5.355h-1.159zm-4.263 0l-1.185 3.367h1.103l1.186-3.367h-1.104zm5.737 2.48c0 .585.502.961 1.258.961.645 0 1.077-.294 1.248-.742l-.91-.378c-.103.19-.318.318-.576.318-.413 0-.688-.24-.729-.627h2.28v-.24c0-.823-.516-1.387-1.414-1.387-.879 0-1.468.64-1.468 1.555 0 .962.621 1.54 1.569 1.54zm-.051-.724c.052-.31.31-.516.621-.516.297 0 .544.19.585.516H20.95z"/>
                <path d="M13.212 6.155c-.666 0-1.094.314-1.282.783h-.019v-.688h-1.034v4.894h1.084V9.522c.188.454.606.758 1.251.758.93 0 1.574-.722 1.574-1.792 0-1.075-.643-1.798-1.574-1.798zm-.249.836c.516 0 .858.405.858.961 0 .552-.342.957-.858.957-.511 0-.853-.4-.853-.957 0-.556.342-.961.853-.961zM9.03 8.493c0-.928-.58-1.543-1.453-1.543-.881 0-1.458.649-1.458 1.553 0 .909.577 1.553 1.523 1.553.532 0 .986-.173 1.293-.506l-.647-.562c-.171.173-.403.266-.611.266-.428 0-.707-.234-.748-.623h2.096l.004-.138zm-1.91-.318c.297 0 .54.182.585.511H6.499c.052-.31.31-.511.621-.511z"/>
                <path d="M5.055 6.327c-.213-.11-.467-.172-.758-.172-.704 0-1.29.383-1.548.91a1.46 1.46 0 00-.126.59c0 .212.042.423.126.591.254.528.844.91 1.548.91.291 0 .545-.061.758-.172v.674c0 .375-.275.576-.632.576-.363 0-.596-.191-.677-.389l-.935.389c.249.515.784.832 1.612.832.93 0 1.648-.516 1.648-1.458V6.77h-1.016v.231h-.025c-.162-.192-.438-.346-.753-.346-.591 0-1.08.44-1.08 1.024 0 .58.489 1.02 1.08 1.02.315 0 .591-.154.753-.342h.025v.136z"/>
              </g>
              <g fill="#4285F4">
                <path d="M31.954 9.197c-.662 0-1.201-.506-1.201-1.2 0-.699.539-1.206 1.201-1.206.658 0 1.177.507 1.177 1.206 0 .694-.519 1.2-1.177 1.2zm0-3.866c-1.487 0-2.696 1.13-2.696 2.666 0 1.531 1.209 2.666 2.696 2.666 1.482 0 2.696-1.135 2.696-2.666 0-1.536-1.214-2.666-2.696-2.666z"/>
              </g>
              <g fill="#34A853">
                <path d="M25.138 9.197c-.662 0-1.201-.506-1.201-1.2 0-.699.539-1.206 1.201-1.206.658 0 1.177.507 1.177 1.206 0 .694-.519 1.2-1.177 1.2zm0-3.866c-1.487 0-2.696 1.13-2.696 2.666 0 1.531 1.209 2.666 2.696 2.666 1.482 0 2.696-1.135 2.696-2.666 0-1.536-1.214-2.666-2.696-2.666z"/>
              </g>
              <g fill="#EA4335">
                <path d="M39.142 9.197c-.643 0-1.105-.516-1.105-1.19 0-.679.462-1.216 1.105-1.216.639 0 1.081.537 1.081 1.216 0 .674-.442 1.19-1.081 1.19zm1.025-3.541h-.996v.365h-.029c-.197-.279-.568-.516-1.043-.516-1.243 0-2.311 1.096-2.311 2.503 0 1.392 1.068 2.483 2.311 2.483.475 0 .846-.237 1.043-.521h.029v.328c0 .824-.452 1.276-1.115 1.276-.56 0-.908-.403-1.052-.742l-.894.373c.256.618.938 1.375 1.946 1.375 1.13 0 2.084-.666 2.084-2.287V5.656h-.973z"/>
              </g>
              <path d="M40.5 10.498h1.004V2.623H40.5v7.875z" fill="#FBBC04"/>
            </svg>
            <span className="font-medium">Pay</span>
          </button>
        </div>
      </div>
      
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">or pay with card</span>
        </div>
      </div>
    </div>
  );
};

export default GooglePayButton;