import { useRef, useState } from "react";
import { SecurePayFieldsProps } from "./types/payfields.types";
import { usePayFields } from "./hooks/usePayFields";
import GooglePayButton from "./GooglePayButton";

const SecurePayFields = ({ config, paymentInfo, onSuccess, onFailure, className = "", billingAddress }: SecurePayFieldsProps) => {
  const cardNumberRef = useRef<HTMLDivElement>(null);
  const cardNameRef = useRef<HTMLDivElement>(null);
  const cardCvvRef = useRef<HTMLDivElement>(null);
  const cardExpirationRef = useRef<HTMLDivElement>(null);
  const [showCardFields] = useState(true);

  const { scriptError, isSubmitting, validationError, handleSubmit, handleGooglePaySubmit } = usePayFields({
    config,
    paymentInfo,
    billingAddress,
    onSuccess,
    onFailure,
  });

  if (scriptError) {
    return (
      <div className={`p-4 bg-red-50 text-red-800 rounded-md ${className}`}>
        <p>{scriptError}</p>
      </div>
    );
  }

  if (!config) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading secure payment form...</p>
      </div>
    );
  }

  return (
    <div className={`secure-payfields ${className}`}>
      {validationError && (
        <div className="p-4 mb-4 bg-red-50 text-red-800 rounded-md">
          <p>{validationError}</p>
        </div>
      )}

      <div className="space-y-4">
        {/* Google Pay Button */}
        {config.googlePayEnabled && (
          <GooglePayButton
            onClick={handleGooglePaySubmit}
            disabled={isSubmitting}
            amount={paymentInfo?.amount || config.amount}
            containerClassName="mb-4"
          />
        )}

        {/* Card Fields */}
        <div className={config.googlePayEnabled && !showCardFields ? "hidden" : ""}>
          <div className="mb-4">
            <label htmlFor="card-number" className="block mb-2 text-sm font-medium text-gray-700">
              Card Number
            </label>
            <div id="card-number" ref={cardNumberRef} className="h-12 border rounded-md"></div>
          </div>

          <div className="mb-4">
            <label htmlFor="card-name" className="block mb-2 text-sm font-medium text-gray-700">
              Cardholder Name
            </label>
            <div id="card-name" ref={cardNameRef} className="h-12 border rounded-md"></div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="card-expiration" className="block mb-2 text-sm font-medium text-gray-700">
                Expiration Date
              </label>
              <div id="card-expiration" ref={cardExpirationRef} className="h-12 border rounded-md"></div>
            </div>
            <div>
              <label htmlFor="card-cvv" className="block mb-2 text-sm font-medium text-gray-700">
                CVV
              </label>
              <div id="card-cvv" ref={cardCvvRef} className="h-12 border rounded-md"></div>
            </div>
          </div>
        </div>

        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className={`w-full py-3 px-4 font-medium rounded-md transition-all ${
            isSubmitting ? "bg-gray-400 cursor-not-allowed text-white" : "bg-[#364F6B] hover:bg-[#2A3F59] text-white shadow-md hover:shadow-lg"
          }`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </span>
          ) : (
            `Pay $${((paymentInfo?.amount || config.amount) / 100).toFixed(2)}`
          )}
        </button>
      </div>
    </div>
  );
};

export default SecurePayFields;
