import { toast } from "sonner";
import { postMessageToParent } from "./iframe-communication";

export interface GooglePayError {
  statusCode?: string;
  statusMessage?: string;
  intent?: string;
  reason?: string;
  message?: string;
  details?: unknown;
}

export interface GooglePayErrorHandlerOptions {
  onError?: (error: GooglePayError) => void;
  showToast?: boolean;
  logError?: boolean;
}

export const GOOGLE_PAY_ERROR_CODES = {
  BUYER_ACCOUNT_ERROR: "BUYER_ACCOUNT_ERROR",
  BUYER_ACCOUNT_OPTOUT: "BUYER_ACCOUNT_OPTOUT", 
  BUYER_ACCOUNT_INACCESSIBLE: "BUYER_ACCOUNT_INACCESSIBLE",
  MERCHANT_ACCOUNT_ERROR: "MERCHANT_ACCOUNT_ERROR",
  UNSERVICEABLE_LOCATION: "UNSERVICEABLE_LOCATION",
  UNSUPPORTED_API_VERSION: "UNSUPPORTED_API_VERSION",
  UNSUPPORTED_INTENT: "UNSUPPORTED_INTENT",
  INVALID_REQUEST: "INVALID_REQUEST",
  PAYMENT_DATA_INVALID: "PAYMENT_DATA_INVALID",
  TXN_AMOUNT_INVALID: "TXN_AMOUNT_INVALID",
  GENERIC_ERROR: "GENERIC_ERROR",
  INTERNAL_ERROR: "INTERNAL_ERROR",
  DEVELOPER_ERROR: "DEVELOPER_ERROR",
  CANCELED: "CANCELED",
} as const;

export const GOOGLE_PAY_ERROR_MESSAGES = {
  [GOOGLE_PAY_ERROR_CODES.BUYER_ACCOUNT_ERROR]: "There was an issue with your Google Pay account. Please try a different payment method.",
  [GOOGLE_PAY_ERROR_CODES.BUYER_ACCOUNT_OPTOUT]: "Google Pay is not available for your account. Please use a different payment method.",
  [GOOGLE_PAY_ERROR_CODES.BUYER_ACCOUNT_INACCESSIBLE]: "Your Google Pay account is temporarily unavailable. Please try again later.",
  [GOOGLE_PAY_ERROR_CODES.MERCHANT_ACCOUNT_ERROR]: "Google Pay is temporarily unavailable. Please use a different payment method.",
  [GOOGLE_PAY_ERROR_CODES.UNSERVICEABLE_LOCATION]: "Google Pay is not available in your location. Please use a different payment method.",
  [GOOGLE_PAY_ERROR_CODES.UNSUPPORTED_API_VERSION]: "Google Pay integration error. Please contact support.",
  [GOOGLE_PAY_ERROR_CODES.UNSUPPORTED_INTENT]: "Google Pay payment type not supported. Please use a different payment method.",
  [GOOGLE_PAY_ERROR_CODES.INVALID_REQUEST]: "Invalid Google Pay request. Please try again.",
  [GOOGLE_PAY_ERROR_CODES.PAYMENT_DATA_INVALID]: "Google Pay payment data is invalid. Please try again.",
  [GOOGLE_PAY_ERROR_CODES.TXN_AMOUNT_INVALID]: "Invalid payment amount for Google Pay. Please try again.",
  [GOOGLE_PAY_ERROR_CODES.GENERIC_ERROR]: "Google Pay encountered an error. Please try again.",
  [GOOGLE_PAY_ERROR_CODES.INTERNAL_ERROR]: "Google Pay service error. Please try again later.",
  [GOOGLE_PAY_ERROR_CODES.DEVELOPER_ERROR]: "Google Pay configuration error. Please contact support.",
  [GOOGLE_PAY_ERROR_CODES.CANCELED]: "Google Pay payment was canceled.",
} as const;

export const handleGooglePayError = (
  error: GooglePayError | unknown,
  options: GooglePayErrorHandlerOptions = {}
): string => {
  const { onError, showToast = true, logError = true } = options;

  let errorCode: string;
  let errorMessage: string;
  let userMessage: string;

  if (error && typeof error === "object" && "statusCode" in error) {
    const googlePayError = error as GooglePayError;
    errorCode = googlePayError.statusCode || "UNKNOWN_ERROR";
    errorMessage = googlePayError.statusMessage || googlePayError.message || "Unknown Google Pay error";
    
    // Get user-friendly message
    userMessage = GOOGLE_PAY_ERROR_MESSAGES[errorCode as keyof typeof GOOGLE_PAY_ERROR_MESSAGES] || 
                  "Google Pay encountered an error. Please try a different payment method.";
  } else if (error instanceof Error) {
    errorCode = "JAVASCRIPT_ERROR";
    errorMessage = error.message;
    userMessage = "Google Pay encountered an error. Please try again.";
  } else {
    errorCode = "UNKNOWN_ERROR";
    errorMessage = String(error);
    userMessage = "Google Pay encountered an error. Please try a different payment method.";
  }

  if (logError) {
    console.error("Google Pay Error:", {
      code: errorCode,
      message: errorMessage,
      userMessage,
      originalError: error,
    });
  }

  if (showToast && errorCode !== GOOGLE_PAY_ERROR_CODES.CANCELED) {
    toast.error(userMessage);
  }

  // Post message to parent for iframe communication
  postMessageToParent("GOOGLEPAY_ERROR", {
    code: errorCode,
    message: errorMessage,
    userMessage,
    details: error,
  });

  if (onError) {
    onError({
      statusCode: errorCode,
      statusMessage: errorMessage,
      message: userMessage,
      details: error,
    });
  }

  return userMessage;
};

export const handleGooglePayScriptLoadError = (
  error: Error | Event,
  options: GooglePayErrorHandlerOptions = {}
): string => {
  const errorMessage = "Failed to load Google Pay. Please refresh the page and try again.";
  
  console.error("Google Pay Script Load Error:", error);
  
  if (options.showToast !== false) {
    toast.error(errorMessage);
  }

  postMessageToParent("GOOGLEPAY_SCRIPT_ERROR", {
    error: errorMessage,
    details: error,
  });

  if (options.onError) {
    options.onError({
      statusCode: "SCRIPT_LOAD_ERROR",
      message: errorMessage,
      details: error,
    });
  }

  return errorMessage;
};

export const handleGooglePayInitializationError = (
  error: unknown,
  options: GooglePayErrorHandlerOptions = {}
): string => {
  const errorMessage = "Google Pay initialization failed. Please try a different payment method.";
  
  console.error("Google Pay Initialization Error:", error);
  
  if (options.showToast !== false) {
    toast.error(errorMessage);
  }

  postMessageToParent("GOOGLEPAY_INIT_ERROR", {
    error: errorMessage,
    details: error,
  });

  if (options.onError) {
    options.onError({
      statusCode: "INITIALIZATION_ERROR",
      message: errorMessage,
      details: error,
    });
  }

  return errorMessage;
};

export const handleGooglePayTimeoutError = (
  timeoutMs: number = 30000,
  options: GooglePayErrorHandlerOptions = {}
): string => {
  const errorMessage = `Google Pay payment timed out after ${timeoutMs / 1000} seconds. Please try again.`;
  
  console.warn("Google Pay Timeout:", { timeoutMs });
  
  if (options.showToast !== false) {
    toast.error(errorMessage);
  }

  postMessageToParent("GOOGLEPAY_TIMEOUT", {
    error: errorMessage,
    timeoutMs,
  });

  if (options.onError) {
    options.onError({
      statusCode: "TIMEOUT_ERROR",
      message: errorMessage,
      details: { timeoutMs },
    });
  }

  return errorMessage;
};

export const isGooglePayAvailable = (): boolean => {
  return !!(window.google && window.google.payments && window.google.payments.api);
};

export const validateGooglePayConfiguration = (config: unknown): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== "object") {
    return { isValid: false, error: "Google Pay configuration is missing or invalid" };
  }

  const googlePayConfig = config as Record<string, unknown>;

  if (!googlePayConfig.environment || !["TEST", "PRODUCTION"].includes(googlePayConfig.environment as string)) {
    return { isValid: false, error: "Invalid Google Pay environment. Must be 'TEST' or 'PRODUCTION'" };
  }

  if (!googlePayConfig.merchantId || typeof googlePayConfig.merchantId !== "string") {
    return { isValid: false, error: "Google Pay merchant ID is required" };
  }

  if (!googlePayConfig.merchantName || typeof googlePayConfig.merchantName !== "string") {
    return { isValid: false, error: "Google Pay merchant name is required" };
  }

  return { isValid: true };
};
