# Google Pay Integration Testing Guide

## Overview
This guide provides comprehensive testing procedures for the Google Pay integration with Payrix PayFields, following the Worldpay documentation requirements.

## Prerequisites

### 1. Google Pay Test Environment Setup
- Join <PERSON>'s test card suite group: [Google Pay & Wallet Console](https://pay.google.com/business/console)
- Ensure your email is added to receive access to test cards
- Use Google Pay test environment (`environment: "TEST"`)

### 2. Required Test Cards
Google Pay requires specific test cards from their test suite:
- **Visa**: ****************
- **Mastercard**: ****************
- **American Express**: ***************
- **Discover**: ****************

### 3. Browser Requirements
- Chrome 61+ (recommended for Google Pay testing)
- Safari 11.1+ (iOS Safari for mobile testing)
- Firefox 56+ (limited Google Pay support)

## Testing Scenarios

### 1. Component Rendering Tests

#### Test 1.1: Google Pay Button Visibility
**Objective**: Verify Google Pay button renders when enabled
**Steps**:
1. Navigate to payment page with Google Pay enabled
2. Verify "Express Checkout" section appears
3. Verify Google Pay button with Google logo is visible
4. Verify "or pay with card" separator appears

**Expected Result**: Google Pay button displays above card fields

#### Test 1.2: Google Pay Button Hidden
**Objective**: Verify Google Pay button doesn't render when disabled
**Steps**:
1. Set `googlePayEnabled: false` in configuration
2. Navigate to payment page
3. Verify no "Express Checkout" section
4. Verify only card fields are visible

**Expected Result**: Only traditional card fields display

### 2. Configuration Tests

#### Test 2.1: Valid Configuration
**Test Configuration**:
```javascript
{
  googlePayEnabled: true,
  googlePayEnvironment: "TEST",
  googlePayMerchantId: "12345678901234567890",
  googlePayMerchantName: "Auth-Clear Test",
  googlePayConfig: {
    enabled: true,
    environment: "TEST",
    merchantId: "12345678901234567890",
    merchantName: "Auth-Clear Test",
    allowedCardNetworks: ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
    allowedAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
    gateway: "payrix",
    gatewayMerchantId: "your_payrix_merchant_id"
  }
}
```

**Expected Result**: Google Pay button renders successfully

#### Test 2.2: Invalid Configuration
**Test Scenarios**:
- Missing `merchantId`
- Invalid `environment` (not "TEST" or "PRODUCTION")
- Empty `merchantName`

**Expected Result**: Error handling prevents button rendering, shows fallback to card fields

### 3. Script Loading Tests

#### Test 3.1: Google Pay Script Load Success
**Steps**:
1. Open browser developer tools
2. Navigate to payment page
3. Check Network tab for `https://pay.google.com/gp/p/js/pay.js`
4. Verify script loads successfully (200 status)

**Expected Result**: Google Pay script loads without errors

#### Test 3.2: Google Pay Script Load Failure
**Steps**:
1. Block `pay.google.com` domain in browser
2. Navigate to payment page
3. Verify error handling

**Expected Result**: Error message displayed, fallback to card fields

### 4. Payment Flow Tests

#### Test 4.1: Google Pay Token Generation
**Objective**: Verify token-based payment flow
**Steps**:
1. Click Google Pay button
2. Complete Google Pay flow with test card
3. Verify token generation (mode: "token", txnType: "auth", amount: "0")
4. Check browser console for token details

**Expected Result**: 
- PayFields generates secure token
- No actual charge processed (amount: "0")
- Token can be used for backend processing

#### Test 4.2: Google Pay Payment Cancellation
**Steps**:
1. Click Google Pay button
2. Cancel Google Pay dialog
3. Verify graceful handling

**Expected Result**: No error messages, user can retry or use card fields

### 5. Error Handling Tests

#### Test 5.1: Google Pay Not Available
**Simulation**: Use browser that doesn't support Google Pay
**Expected Result**: Button doesn't render, card fields available

#### Test 5.2: Google Pay Account Issues
**Simulation**: Use account without Google Pay setup
**Expected Result**: Appropriate error message, fallback to card fields

#### Test 5.3: Network Connectivity Issues
**Simulation**: Disconnect internet during Google Pay flow
**Expected Result**: Timeout handling, error message, retry option

### 6. Mobile Testing

#### Test 6.1: iOS Safari
**Steps**:
1. Test on iPhone with Safari
2. Verify Google Pay button renders
3. Test payment flow with Apple Pay fallback

#### Test 6.2: Android Chrome
**Steps**:
1. Test on Android device with Chrome
2. Verify native Google Pay integration
3. Test payment flow with saved cards

### 7. Integration Testing

#### Test 7.1: PayFields Integration
**Verification Points**:
- Google Pay button added to PayFields.fields array
- PayFields.config.googlePay properly configured
- PayFields.submit() triggers Google Pay flow

#### Test 7.2: Backend Token Processing
**Steps**:
1. Complete Google Pay token generation
2. Verify token sent to backend
3. Verify backend processes token via Payrix API
4. Verify token cleanup after processing

## Test Data

### Environment Variables
```bash
VITE_PAYRIX_PAYMENT_URL=https://test-api.payrix.com/payFieldsScript
PAYRIX_PUBLIC_API_KEY=pk_test_your_key_here
```

### Test Merchant Configuration
```javascript
{
  merchantId: "t1_mer_68af654249bb62256090226",
  description: "Google Pay Test Transaction",
  amount: 0, // $0 authorization for tokenization
  googlePayEnabled: true,
  googlePayEnvironment: "TEST",
  googlePayMerchantId: "12345678901234567890",
  googlePayMerchantName: "Auth-Clear Test"
}
```

## Debugging

### Console Logging
Monitor browser console for:
- Google Pay script loading messages
- PayFields configuration logs
- Token generation details
- Error messages

### Network Monitoring
Check Network tab for:
- Google Pay script requests
- PayFields API calls
- Token generation requests
- Backend processing calls

### Common Issues

#### Issue 1: Google Pay Button Not Appearing
**Causes**:
- Script loading failure
- Invalid configuration
- Browser compatibility

**Solutions**:
- Check network connectivity
- Verify configuration parameters
- Test in supported browser

#### Issue 2: Google Pay Flow Fails
**Causes**:
- Invalid merchant ID
- Test card not in Google Pay test suite
- Network issues

**Solutions**:
- Verify Google Pay merchant ID
- Use approved test cards
- Check network connectivity

## Production Checklist

Before going live:
- [ ] Update `googlePayEnvironment` to "PRODUCTION"
- [ ] Replace test merchant ID with production ID
- [ ] Update PayFields script URL to production
- [ ] Verify domain registration with Google Pay
- [ ] Test with real Google Pay accounts
- [ ] Verify PCI compliance maintained

## Success Criteria

✅ Google Pay button renders when enabled
✅ Google Pay button hidden when disabled  
✅ Token generation works with $0 authorization
✅ Error handling prevents crashes
✅ Fallback to card fields works
✅ Mobile compatibility verified
✅ Backend token processing successful
✅ PCI compliance maintained
