# Google Pay Integration with Payrix PayFields

## Overview
This document outlines the Google Pay integration implementation following the Payrix PayFields digital wallet patterns as specified in their documentation.

## Implementation Details

### 1. PayFields Configuration Structure
Following the official Payrix documentation, Google Pay is configured through the `window.PayFields.config.googlePay` object:

```javascript
window.PayFields.config.googlePay = {
  environment: 'TEST', // or 'PRODUCTION'
  merchantId: 'Your-Google-Pay-Merchant-ID',
  merchantName: 'Your Merchant Name',
  allowedCardNetworks: ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'],
  allowedAuthMethods: ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
  gateway: 'payrix',
  gatewayMerchantId: 'Your-Payrix-Merchant-ID'
}
```

### 2. PayFields Element Configuration
The Google Pay button is added as a field in the PayFields configuration:

```javascript
PayFields.fields = [
  { type: "number", element: "#card-number" },
  { type: "name", element: "#card-name" },
  { type: "cvv", element: "#card-cvv" },
  { type: "expiration", element: "#card-expiration" },
  { type: "googlePay", element: "#googlePayButton" } // Google Pay button
]
```

### 3. Token-Only Mode Configuration
The implementation maintains PCI compliance by using token-only mode with $0 authorization:

```javascript
mode: 'token'
txnType: 'auth'
amount: '0'
```

### 4. Environment Variables
Configure these in your `.env` file:

```bash
VITE_GOOGLE_PAY_ENABLED=true
VITE_GOOGLE_PAY_ENVIRONMENT=TEST
VITE_GOOGLE_PAY_MERCHANT_ID=BCR2DN6TQZ7XBHLH
VITE_GOOGLE_PAY_MERCHANT_NAME=Auth-Clear
```

### 5. File Structure

#### Core Files Modified:
- `payfields.types.ts` - Updated PayFieldsWindow interface with Google Pay configuration
- `payfields-initializer.ts` - Added Google Pay configuration logic
- `GooglePayButton.tsx` - New component for Google Pay button UI
- `SecurePayFields.tsx` - Integrated Google Pay button
- `usePayFields.ts` - Added Google Pay submission handler
- `PaymentLogos.tsx` - Added Google Pay logo
- `payment.ts` - Updated PayFieldsConfig type

### 6. Key Features Implemented

#### Required Parameters (per Payrix documentation):
- ✅ `environment` - TEST/PRODUCTION
- ✅ `merchantId` - Google Pay merchant identifier
- ✅ `merchantName` - Display name in Google Pay dialog
- ✅ `allowedCardNetworks` - Supported card brands
- ✅ `allowedAuthMethods` - Authentication types
- ✅ `gateway` - Set to 'payrix'
- ✅ `gatewayMerchantId` - Payrix merchant ID

#### Integration Features:
- ✅ Token-only mode for PCI compliance
- ✅ $0 authorization for secure tokenization
- ✅ Fallback to card payment when Google Pay unavailable
- ✅ Proper event handling for Google Pay transactions
- ✅ Mobile-responsive design
- ✅ Error handling and user feedback

### 7. Testing Checklist

#### Development Environment:
- [ ] Verify Google Pay button appears when configured
- [ ] Test with Google's test card suite
- [ ] Confirm token generation with $0 authorization
- [ ] Test fallback to card fields when Google Pay unavailable
- [ ] Verify error handling for failed Google Pay attempts

#### Production Requirements:
- [ ] Register domain with Google Pay & Wallet Console
- [ ] Obtain production Google Pay Merchant ID
- [ ] Update environment to PRODUCTION
- [ ] Remove test configurations
- [ ] Test with real Google Pay accounts

### 8. Google Pay Setup Process

1. **Register with Google Pay:**
   - Go to [Google Pay & Wallet Console](https://pay.google.com/business/console)
   - Register your business
   - Verify your domain

2. **Configure Payrix Merchant:**
   - Ensure digital wallets are enabled in Payrix merchant settings
   - Obtain Payrix merchant ID for gateway configuration

3. **Update Configuration:**
   - Set `VITE_GOOGLE_PAY_ENVIRONMENT=PRODUCTION`
   - Update `VITE_GOOGLE_PAY_MERCHANT_ID` with production ID
   - Deploy changes

### 9. Security Considerations

- Never log sensitive payment information
- Always use HTTPS in production
- Tokens are generated with $0 authorization for security
- No actual card data is handled by the frontend
- All payment processing happens through secure PayFields iframes

### 10. Troubleshooting

#### Google Pay button not appearing:
- Verify merchant configuration in Payrix
- Check browser compatibility (Chrome, Safari)
- Ensure Google Pay is available in user's region
- Check console for PayFields initialization errors

#### Token generation fails:
- Verify Payrix merchant ID is correct
- Check API key validity
- Ensure proper environment (TEST vs PRODUCTION)
- Review browser console for specific error messages

## References
- [Payrix PayFields Documentation](https://resource.payrix.com/docs/integrated-payment-pages)
- [Payrix Digital Wallet Payments](https://resource.payrix.com/docs/payframe-digital-wallet-payments)
- [Google Pay Web Integration Guide](https://developers.google.com/pay/api/web/guides/tutorial)